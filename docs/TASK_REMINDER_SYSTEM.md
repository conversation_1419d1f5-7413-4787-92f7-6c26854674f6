# Task Reminder System

## Overview

The Task Reminder System automatically sends WhatsApp notifications to assigned users before task deadlines. The system sends reminders at 7 days, 3 days, and 1 day before the deadline.

## Features

### 🔔 Automatic Reminders
- **7-day reminder**: Sent 7 days before deadline (if deadline is more than 7 days away)
- **3-day reminder**: Sent 3 days before deadline (if deadline is more than 3 days away)
- **1-day reminder**: Sent 1 day before deadline (if deadline is more than 1 day away)
- **User preference**: Users can opt-in/out of reminders when creating tasks (enabled by default)

### 📱 WhatsApp Integration
- Uses company's Green API credentials
- Sends formatted messages with task details
- Includes priority indicators and deadline information

### 🎯 Smart Targeting
- Sends reminders to task assigned user (if specified)
- Falls back to case assigned user if no task assignee
- Includes user's name in personalized messages

## Database Schema

### task_reminders Table
```sql
- id: UUID (Primary Key)
- task_id: UUID (Foreign Key to case_tasks)
- company_id: UUID (Company identifier)
- reminder_type: TEXT ('7_days', '3_days', '1_day')
- scheduled_for: TIMESTAMP (When to send the reminder)
- sent_at: TIMESTAMP (When reminder was sent)
- status: TEXT ('pending', 'sent', 'failed', 'cancelled')
- error_message: TEXT (Error details if failed)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### case_tasks Table Updates
```sql
- reminders_enabled: BOOLEAN (Whether reminders are enabled for this task, default: true)
```

### Automatic Triggers
- **Task Creation**: Automatically creates reminders when task with deadline is created and reminders are enabled
- **Task Update**: Updates reminders when deadline or reminder preference changes
- **Task Completion**: Cancels pending reminders when task is completed
- **Task Deletion**: Removes all associated reminders
- **Reminder Preference**: Only creates reminders if `reminders_enabled` is true

## UI Improvements

### Enhanced Date/Time Picker
- **Visual Calendar**: Easy date selection with Hebrew locale
- **Time Presets**: Quick selection for common times (9:00, 12:00, 14:00, 17:00)
- **Quick Actions**: "Today" and "Tomorrow" buttons
- **Clear Display**: Shows selected date/time in readable format
- **Validation**: Prevents selecting past dates

### Task Management
- **Edit Tasks**: Click edit button to modify existing tasks
- **Visual Priorities**: Color-coded priority indicators
- **Deadline Status**: Visual indicators for overdue/urgent tasks
- **Assigned Users**: Shows who is responsible for each task
- **Status Management**: Easy status updates with visual feedback
- **Reminder Preference Toggle**: Users can enable/disable WhatsApp reminders (default: enabled)
- **Unified Creation/Editing**: Same UI for both task creation and editing
- **Reminder Status Indicators**: Bell icons show reminder status in task list (🔔 enabled, 🔕 disabled)

### Task Display
- **Priority Colors**: 
  - 🔴 Critical (Red)
  - 🟠 High (Orange) 
  - 🟡 Medium (Yellow)
  - 🟢 Low (Green)
- **Deadline Warnings**:
  - Red: Overdue tasks
  - Orange: Due within 24 hours
  - Yellow: Due within 72 hours
- **Status Badges**: Clear visual status indicators

## Edge Functions

### task-reminder-processor
**Purpose**: Processes pending reminders and sends WhatsApp messages

**Endpoint**: `/functions/v1/task-reminder-processor`

**Functionality**:
1. Queries pending reminders from `pending_task_reminders` view
2. Gets user profile and phone number
3. Creates personalized reminder message
4. Sends WhatsApp message via Green API
5. Updates reminder status to 'sent' or 'failed'

### workflow-scheduler (Enhanced)
**Purpose**: Periodically calls the task reminder processor

**Schedule**: Runs every few minutes via cron job

**Integration**: Calls `task-reminder-processor` function as part of scheduled workflow

## Message Format

Reminder messages include:
- **Priority Emoji**: Visual priority indicator
- **User Name**: Personalized greeting
- **Task Title**: Clear task identification
- **Case Title**: Context about the related case
- **Deadline**: Formatted date and time
- **Priority Level**: Text priority level
- **Description**: Task details (if provided)
- **Encouragement**: Motivational closing

Example message:
```
🟠 תזכורת משימה - בעוד 3 ימים

שלום יוסי כהן,

יש לך משימה שתסתיים בעוד 3 ימים:

📋 *משימה:* הכנת כתב תביעה
📁 *תיק:* תיק גירושין - משפחת לוי
⏰ *תאריך יעד:* 15/01/2024 14:00
🎯 *עדיפות:* גבוה

📝 *פרטים:* הכנת כתב תביעה למזונות ילדים

אנא וודא שהמשימה תושלם בזמן.

בהצלחה! 💪
```

## Configuration

### Prerequisites
1. **Green API Setup**: Company must have valid Green API credentials
2. **User Profiles**: Users must have phone numbers in their profiles
3. **Task Assignment**: Tasks should have assigned users for targeted reminders

### Environment Variables
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for database access

## Monitoring

### Reminder Status Tracking
- **Pending**: Reminder scheduled but not yet sent
- **Sent**: Reminder successfully delivered
- **Failed**: Reminder failed to send (with error message)
- **Cancelled**: Reminder cancelled (task completed/deleted)

### Error Handling
- Failed reminders are logged with error messages
- System continues processing other reminders if one fails
- Automatic retry logic for transient failures

## Usage

### Creating Tasks with Reminders
1. Open task creation modal
2. Fill in task details
3. Select deadline using enhanced date/time picker
4. Assign user (optional but recommended)
5. Save task - reminders are automatically created

### Editing Tasks
1. Click edit button on any task
2. Modify task details as needed
3. Update deadline - reminders are automatically adjusted
4. Save changes

### Managing Reminders
- Reminders are automatically managed by the system
- Completing a task cancels all pending reminders
- Deleting a task removes all associated reminders
- Changing deadlines updates reminder schedules

## Troubleshooting

### Common Issues
1. **No reminders sent**: Check Green API credentials in company settings
2. **User not receiving messages**: Verify user has phone number in profile
3. **Wrong timing**: Check server timezone and reminder scheduling
4. **Failed reminders**: Check error messages in task_reminders table

### Debugging
- Check `task_reminders` table for reminder status
- Review Edge Function logs in Supabase dashboard
- Verify Green API credentials and phone number formatting
- Test with `pending_task_reminders` view to see due reminders
