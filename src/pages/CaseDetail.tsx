import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowRight,
  Plus,
  Play,
  Pause,
  Clock,
  Upload,
  Download,
  FileText,
  CheckCircle2,
  Loader2,
  Edit
} from "lucide-react";
import { TasksSection } from "@/components/case/TasksSection";
import { DocumentsSection } from "@/components/case/DocumentsSection";
import { TimeTrackingSection } from "@/components/case/TimeTrackingSection";
import { EditCaseModal } from "@/components/case/EditCaseModal";
import { useCases } from "@/hooks/useCases";
import { useToast } from "@/hooks/use-toast";

const CaseDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [caseData, setCaseData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { getCaseById, updateCase } = useCases();
  const { toast } = useToast();

  useEffect(() => {
    const fetchCase = async () => {
      if (!id) return;

      setIsLoading(true);
      try {
        const data = await getCaseById(id);
        if (data) {
          setCaseData(data);
        } else {
          toast({
            title: "שגיאה",
            description: "התיק לא נמצא",
            variant: "destructive",
          });
          navigate('/cases');
        }
      } catch (error) {
        console.error('Error fetching case:', error);
        toast({
          title: "שגיאה",
          description: "שגיאה בטעינת התיק",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCase();
  }, [id, getCaseById]);

  const handleStatusChange = async (newStatus: string) => {
    if (!caseData) return;

    try {
      await updateCase(caseData.id, { status: newStatus });
      setCaseData(prev => prev ? { ...prev, status: newStatus } : null);
      toast({
        title: "הצלחה",
        description: "סטטוס התיק עודכן בהצלחה",
      });
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון סטטוס התיק",
        variant: "destructive",
      });
    }
  };

  const handleCaseUpdate = (updatedCase: any) => {
    setCaseData(updatedCase);
  };

  const refetchCaseData = async () => {
    if (!id) return;

    try {
      const data = await getCaseById(id);
      if (data) {
        setCaseData(data);
      }
    } catch (error) {
      console.error('Error refetching case data:', error);
    }
  };

  const calculateAverageHourlyRate = () => {
    if (!caseData?.value || !caseData?.total_time_logged || caseData.total_time_logged === 0) {
      return 0;
    }
    const hours = caseData.total_time_logged / 60;
    return Math.round(caseData.value / hours);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: 'ILS'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "בקליטה":
        return "bg-warning/10 text-warning border-warning/20";
      case "פתוח":
        return "bg-success/10 text-success border-success/20";
      case "סגור":
        return "bg-muted text-muted-foreground border-border";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (!caseData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">התיק לא נמצא</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <button 
          onClick={() => navigate('/cases')}
          className="hover:text-foreground transition-colors flex items-center gap-1"
        >
          ניהול תיקים
          <ArrowRight className="w-4 h-4" />
        </button>
        <span>תיק מספר {id}</span>
      </div>

      {/* Case Header */}
      <Card className="card-professional">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-bold text-foreground">{caseData.title}</h1>
                <Badge className={getStatusColor(caseData.status)}>
                  {caseData.status}
                </Badge>
              </div>
              <p className="text-muted-foreground">{caseData.description || "אין תיאור זמין"}</p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                עריכה
              </Button>
              <span className="text-sm font-medium text-muted-foreground">סטטוס התיק:</span>
              <Select value={caseData.status} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="בקליטה">בקליטה</SelectItem>
                  <SelectItem value="פתוח">פתוח</SelectItem>
                  <SelectItem value="סגור">סגור</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
            <div>
              <span className="text-sm font-medium text-muted-foreground">לקוח</span>
              <p className="text-foreground font-medium">{caseData.lead?.full_name || "לא צוין"}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">סוג התיק</span>
              <p className="text-foreground font-medium">{caseData.case_type?.name || "לא צוין"}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">דדליין</span>
              <p className="text-foreground font-medium">
                {caseData.deadline ? new Date(caseData.deadline).toLocaleDateString('he-IL') : "לא צוין"}
              </p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">ערך התיק</span>
              <p className="text-foreground font-medium">
                {caseData.value ? formatCurrency(caseData.value) : "לא צוין"}
              </p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">תעריף ממוצע</span>
              <p className="text-foreground font-medium">
                {calculateAverageHourlyRate() > 0 ? `${calculateAverageHourlyRate()}₪/שעה` : "לא זמין"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Time Tracking Section */}
      <TimeTrackingSection caseId={id!} caseType={caseData.case_type} onTimeEntryUpdated={refetchCaseData} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tasks Section */}
        <TasksSection caseId={id} onTaskUpdated={refetchCaseData} />

        {/* Documents Section */}
        <DocumentsSection caseId={id} />
      </div>

      {/* Edit Case Modal */}
      <EditCaseModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        caseData={caseData}
        onUpdate={handleCaseUpdate}
      />
    </div>
  );
};

export default CaseDetail;