# Workflow Functionality Test Plan

## Overview
This document outlines the testing plan for the new WhatsApp workflow functionality that allows sending messages to either leads or assigned users.

## Changes Made

### 1. Database Schema
- Added `assigned_user_id` field to the `cases` table
- Made assigned user mandatory for case creation

### 2. UI Changes
- Updated case creation form to require assigned user selection
- Added recipient selection (radio buttons) to WhatsApp workflow action configuration
- Added validation for both assigned user and recipient selection

### 3. Backend Changes
- Modified `executeSendWhatsApp` function to handle both lead and assigned user recipients
- Added logic to retrieve assigned user phone numbers from auth.users
- Updated conversation creation to handle messages to assigned users

## Test Scenarios

### Scenario 1: Lead Status Change Workflow with Lead Recipient
1. Create a workflow triggered by lead status change
2. Add WhatsApp action with recipient set to "lead"
3. Test that message is sent to the lead's phone number

### Scenario 2: Lead Status Change Workflow with Assigned User Recipient
1. Create a workflow triggered by lead status change
2. Ensure lead has an assigned user with a phone number
3. Add WhatsApp action with recipient set to "assigned user"
4. Test that message is sent to the assigned user's phone number

### Scenario 3: Case Status Change Workflow with Lead Recipient
1. Create a case with an assigned user
2. Create a workflow triggered by case status change
3. Add WhatsApp action with recipient set to "lead"
4. Test that message is sent to the case's associated lead

### Scenario 4: Case Status Change Workflow with Assigned User Recipient
1. Create a case with an assigned user
2. Create a workflow triggered by case status change
3. Add WhatsApp action with recipient set to "assigned user"
4. Test that message is sent to the case's assigned user

## Expected Behavior

### For Lead Recipients:
- Message should be sent to the lead's phone number
- Conversation should be created/updated with the lead
- Message variables should be populated with lead data

### For Assigned User Recipients:
- Message should be sent to the assigned user's phone number
- Conversation should still be associated with the lead (for context)
- Message variables should be populated with lead data
- Error should be thrown if no assigned user or user has no phone

## Error Handling

### Expected Errors:
1. "No assigned user found for this entity" - when entity has no assigned user
2. "Assigned user not found" - when assigned user ID doesn't exist in auth.users
3. "Assigned user has no phone number" - when user exists but has no phone

## Manual Testing Steps

1. **Setup Test Data:**
   - Create a lead with assigned user
   - Create a case with assigned user
   - Ensure assigned user has phone number in user metadata

2. **Test Workflow Creation:**
   - Navigate to Marketing Automation > Workflows
   - Create new workflow with lead status change trigger
   - Add WhatsApp action and verify recipient selection appears
   - Test validation (should require recipient selection)

3. **Test Workflow Execution:**
   - Trigger workflow by changing lead/case status
   - Verify message is sent to correct recipient
   - Check conversation creation/update
   - Verify message content and variables

## Notes

- The migration for `assigned_user_id` field needs to be applied to the database
- User phone numbers should be stored in auth.users.user_metadata.phone
- Backward compatibility is maintained (default recipient is 'lead')
