# User Management Enhancement Test Plan

## Overview
This document outlines the testing plan for the enhanced user management functionality that now includes phone number support and proper permission controls.

## Changes Made

### 1. Enhanced User Interface
- **Added phone field** to CompanyUser interface
- **Updated user creation form** to include phone number input (optional)
- **Updated user editing form** to include phone number input
- **Added phone column** to users table display

### 2. Backend Enhancements
- **Updated get-company-users function** to retrieve phone numbers from user metadata
- **Enhanced update-user-metadata function** to handle phone number updates
- **Updated create-user-with-password function** to store phone numbers in user metadata

### 3. Permission Controls
- **Added permission checks** to ensure only company admins and super admins can:
  - Create new users
  - Edit user details (name, email, phone, role)
  - Change user roles
  - Delete/deactivate users
- **Regular users** can only view the user list and send password reset emails

### 4. Data Flow
- **Phone numbers** are stored in `auth.users.user_metadata.phone`
- **User data fetching** includes phone from both `user_metadata.phone` and `phone` fields
- **User updates** properly merge with existing metadata

## Test Scenarios

### Scenario 1: Company Admin User Management
1. **Login as company admin**
2. **Navigate to Settings > User Management**
3. **Verify all management options are visible:**
   - "Add User" button should be visible
   - Edit, role change, and delete options should be available in user dropdown menus

### Scenario 2: Regular User Permissions
1. **Login as regular user**
2. **Navigate to Settings > User Management**
3. **Verify limited permissions:**
   - "Add User" button should NOT be visible
   - Only "Send Password Reset" should be available in user dropdown menus
   - Edit, role change, and delete options should NOT be visible

### Scenario 3: Create User with Phone Number
1. **As company admin, click "Add User"**
2. **Fill in all fields including phone number**
3. **Create user and verify:**
   - User appears in list with phone number displayed
   - Phone number is stored correctly in user metadata

### Scenario 4: Edit User Details
1. **As company admin, click edit on a user**
2. **Verify form shows current data including phone**
3. **Update name, email, and phone**
4. **Save and verify:**
   - Changes are reflected in user list
   - Phone number is updated correctly

### Scenario 5: Phone Number Display
1. **View users table**
2. **Verify phone column shows:**
   - Phone numbers for users who have them
   - "לא צוין" (Not specified) for users without phone numbers

## Expected Behavior

### For Company Admins/Super Admins:
- Full access to all user management features
- Can create, edit, and delete users
- Can change user roles
- Can edit all user fields including phone

### For Regular Users:
- Read-only access to user list
- Can only send password reset emails
- Cannot create, edit, or delete users
- Cannot change user roles

### Phone Number Handling:
- Phone numbers are optional during user creation
- Phone numbers can be edited after user creation
- Phone numbers are displayed in the users table
- Phone numbers are stored in user metadata for workflow integration

## Manual Testing Steps

1. **Test Permission Controls:**
   - Login with different user roles
   - Verify appropriate buttons/options are shown/hidden
   - Attempt unauthorized actions (should be prevented)

2. **Test User Creation:**
   - Create user with phone number
   - Create user without phone number
   - Verify both scenarios work correctly

3. **Test User Editing:**
   - Edit user details including phone
   - Verify changes are saved and displayed
   - Test with empty phone number

4. **Test Phone Display:**
   - Check users table shows phone numbers correctly
   - Verify "לא צוין" appears for users without phone

## Integration with Workflow System

The phone numbers stored in user metadata will be used by the workflow system when sending WhatsApp messages to assigned users. This ensures that:

1. **Assigned users can receive workflow notifications**
2. **Phone numbers are centrally managed** in user profiles
3. **Workflow errors are clear** when users don't have phone numbers

## Notes

- Phone numbers are stored in `user_metadata.phone` field
- The system checks both `user_metadata.phone` and `phone` fields for compatibility
- Permission checks use the existing `canManageUsers()` function
- All changes maintain backward compatibility with existing users
