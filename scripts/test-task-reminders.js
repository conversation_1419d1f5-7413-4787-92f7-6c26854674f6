#!/usr/bin/env node

/**
 * Test script for task reminder system
 * This script tests the task reminder functionality by creating test tasks
 * and verifying that reminders are created correctly.
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://jihaizhvpddinhdysscd.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testTaskReminderSystem() {
  console.log('🧪 Testing Task Reminder System...\n');

  try {
    // 1. Test database schema
    console.log('1️⃣ Testing database schema...');
    
    const { data: reminderSchema, error: schemaError } = await supabase
      .from('task_reminders')
      .select('*')
      .limit(1);
    
    if (schemaError) {
      console.error('❌ Task reminders table not found:', schemaError.message);
      return false;
    }
    
    console.log('✅ Task reminders table exists');

    // 2. Test pending reminders view
    console.log('\n2️⃣ Testing pending reminders view...');
    
    const { data: pendingView, error: viewError } = await supabase
      .from('pending_task_reminders')
      .select('*')
      .limit(1);
    
    if (viewError) {
      console.error('❌ Pending task reminders view not found:', viewError.message);
      return false;
    }
    
    console.log('✅ Pending task reminders view exists');

    // 3. Test reminder processor function
    console.log('\n3️⃣ Testing reminder processor function...');
    
    const { data: processorResult, error: processorError } = await supabase.functions.invoke('task-reminder-processor');
    
    if (processorError) {
      console.error('❌ Task reminder processor function failed:', processorError.message);
      return false;
    }
    
    console.log('✅ Task reminder processor function works');
    console.log('📊 Processor result:', processorResult);

    // 4. Test creating a task with deadline (this should trigger reminder creation)
    console.log('\n4️⃣ Testing task creation with reminders...');
    
    // First, get a test company and case
    const { data: companies } = await supabase
      .from('companies')
      .select('id')
      .limit(1);
    
    if (!companies || companies.length === 0) {
      console.log('⚠️ No companies found, skipping task creation test');
      return true;
    }

    const { data: cases } = await supabase
      .from('cases')
      .select('id')
      .eq('company_id', companies[0].id)
      .limit(1);
    
    if (!cases || cases.length === 0) {
      console.log('⚠️ No cases found, skipping task creation test');
      return true;
    }

    // Create a test task with deadline in 10 days
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 10);
    
    const { data: testTask, error: taskError } = await supabase
      .from('case_tasks')
      .insert({
        case_id: cases[0].id,
        title: 'Test Task for Reminders',
        description: 'This is a test task to verify reminder creation',
        deadline: futureDate.toISOString(),
        priority: 'בינוני',
        status: 'לא הושלם',
        company_id: companies[0].id,
        user_id: '00000000-0000-0000-0000-000000000000' // Dummy user ID for test
      })
      .select()
      .single();
    
    if (taskError) {
      console.error('❌ Failed to create test task:', taskError.message);
      return false;
    }
    
    console.log('✅ Test task created:', testTask.title);

    // Check if reminders were created
    const { data: reminders, error: reminderError } = await supabase
      .from('task_reminders')
      .select('*')
      .eq('task_id', testTask.id);
    
    if (reminderError) {
      console.error('❌ Failed to fetch reminders:', reminderError.message);
      return false;
    }
    
    console.log(`✅ ${reminders.length} reminders created for test task`);
    
    if (reminders.length > 0) {
      reminders.forEach(reminder => {
        console.log(`   📅 ${reminder.reminder_type} reminder scheduled for ${reminder.scheduled_for}`);
      });
    }

    // Clean up test task
    await supabase
      .from('case_tasks')
      .delete()
      .eq('id', testTask.id);
    
    console.log('🧹 Test task cleaned up');

    console.log('\n🎉 All tests passed! Task reminder system is working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    return false;
  }
}

// Run the test
testTaskReminderSystem()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
